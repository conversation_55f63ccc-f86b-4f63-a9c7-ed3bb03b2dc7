{"name": "maze-mirror-client", "private": true, "version": "1.5.2", "type": "module", "license": "", "scripts": {"start": "yarn gentypes  && vite --host --port 16661", "dev": "yarn gentypes  && vite --host --port 16661", "dev:test": "yarn gentypes:test  && vite --host  --port 16661 --mode test ", "build": "yarn gentypes && tsc && vite build", "build:test": "yarn gentypes:test && tsc && vite build --mode test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "gentypes": "NODE_ENV=production graphql-codegen --config scripts/codegen.ts", "gentypes:test": "NODE_ENV=test graphql-codegen --config scripts/codegen.ts", "upload": "node scripts/uploadQiniu.js images", "commit": "cz", "prepare": "husky install", "release": "release-it", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx,md,css}": "prettier --write --ignore-unknown"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.1.5", "@react-spring/web": "^9.7.3", "@sentry/react": "^8.47.0", "accurate-core": "^1.0.6", "axios": "^1.8.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "dayjs": "^1.11.11", "html5-qrcode": "^2.3.8", "i18next": "^24.2.2", "image-js": "^0.35.5", "immer": "^10.1.1", "jotai": "^2.8.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.379.0", "qiniu-js": "^3.4.2", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.0", "react-router-dom": "^6.23.1", "react-svg": "^16.3.0", "styled-components": "^6.1.15", "swiper": "^11.2.10", "swr": "^2.3.3", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.168.0", "wujieai-react-icon": "^2.2.13"}, "devDependencies": {"@apollo/client": "^3.10.3", "@apollo/link-error": "^2.0.0-beta.3", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@craco/craco": "^7.1.0", "@eslint/js": "^9.32.0", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.0.6", "@graphql-codegen/typescript-operations": "^4.2.0", "@graphql-codegen/typescript-react-apollo": "^4.3.0", "@release-it/conventional-changelog": "^8.0.1", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/canvas-confetti": "^1.9.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.1", "@types/node": "^20.12.12", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/three": "^0.168.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^4.2.1", "apollo-link-timeout": "^4.0.0", "autoprefixer": "^10.4.19", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "cz-conventional-changelog": "^3.3.0", "eruda": "^3.0.1", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "graphql": "^16.8.1", "husky": "^8.0.1", "lint-staged": "^15.2.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "qiniu": "^7.12.0", "react-scripts": "^5.0.1", "release-it": "^17.2.1", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0", "vite": "^7.1.1", "vite-plugin-checker": "^0.10.2", "vite-plugin-html": "^3.2.2"}, "volta": {"node": "20.11.0", "yarn": "1.22.21", "pnpm": "8.15.7"}}
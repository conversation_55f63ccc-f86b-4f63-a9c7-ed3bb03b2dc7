@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* normal */
  --primary-raw: 191 139 229;
  --primary: #bf8be5;
  --error-raw: 239 70 111;
  --error: #ef466f;
  --success-raw: 69 179 107;
  --success: #45b36b;
  --secondary-raw: 53 57 69;
  --secondary: rgb(53, 57, 69);

  --divider: rgba(0, 0, 0, 0.56);
  --liner-blue-raw: 105 100 222;
  --liner-blue: #6964de;
  --liner-pink-raw: 252 166 233;
  --liner-pink: #fca6e9;

  /* palette */
  --neutral-50-raw: 0 0 0;
  --neutral-50: #000000;
  --neutral-100-raw: 20 20 22;
  --neutral-100: #141416;
  --neutral-200-raw: 35 38 47;
  --neutral-200: #23262f;
  --neutral-300-raw: 53 57 69;
  --neutral-300: #353945;
  --neutral-400-raw: 119 126 144;
  --neutral-400: #777e90;
  --neutral-500-raw: 177 181 196;
  --neutral-500: #b1b5c4;
  --neutral-600-raw: 230 232 236;
  --neutral-600: #e6e8ec;
  --neutral-700-raw: 244 245 246;
  --neutral-700: #f4f5f6;
  --neutral-800-raw: 252 252 253;
  --neutral-800: #fcfcfd;
  --neutral-900-raw: 255 255 255;
  --neutral-900: #ffffff;

  --primary-text: #edf0f4;
  --secondary-text: #b1b5c4;
  --primary-bg: #23283d;
}

@layer base {
  svg {
    vertical-align: unset;
  }
  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: unset;
  }

  body {
    background: #000;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(
      0,
      0,
      0,
      0.6
    ); /* 替换为你想要的颜色，例如 Tailwind 的 slate-400 */
    border-radius: 9999px;
    border: 2px solid transparent;
    background-clip: content-box;
  }
}

/*
 * 注意：大部分自定义工具类已迁移到 tailwind.config.js 中的插件定义
 * 这样可以确保 CSS Modules 中的 @apply 指令能够正确识别这些类
 * 这里只保留一些特殊的样式定义
 */

@layer utilities {
  .mySwiper {
    padding: 16px !important;
    border-radius: 24px;
  }
  .swiper-overflow-visible .mySwiper {
    overflow: visible !important;
  }
  .no-swiper-overflow-visible .mySwiper {
    overflow: hidden !important;
  }
  .maze-slide-active-shadow {
    box-shadow:
      0px 0px 20px 0px rgba(142, 101, 230, 0.4),
      0px 0px 15px 0px rgba(104, 245, 255, 0.8),
      0px 0px 20px 0px #5100ff;
  }
  /* 自定义Swiper Pagination样式 */
  .swiper-pagination-bullet-custom {
    display: inline-block;
    width: 1.25rem !important;
    height: 1.25rem !important;
    background-color: rgba(156, 163, 175, 0.6) !important; /* 灰色 */
    border-radius: 50% !important;
    opacity: 1 !important;
    margin: 0 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
  }

  .swiper-pagination-bullet-active-custom {
    background-color: white !important; /* 白色 */
    transform: scale(1.2) !important;
  }
  .mySwiper .swiper-pagination {
    position: static !important; /* 改为静态定位，不再绝对定位在容器内 */
    margin-top: 2rem !important; /* 添加顶部间距 */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .categoryList {
    border: 2px dashed rgba(255, 255, 255, 0.08);
  }
  .categoryItemActive {
    border-radius: 99px;
    border: 3px solid #edf0f4;
    background: linear-gradient(
      180deg,
      rgba(144, 77, 221, 0.4) 0%,
      rgba(171, 201, 227, 0.4) 100%
    );
    backdrop-filter: blur(5px);
  }
}

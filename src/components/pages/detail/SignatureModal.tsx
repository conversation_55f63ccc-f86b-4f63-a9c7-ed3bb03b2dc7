import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { useRef, useState } from 'react'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { publicPreLoadSourceObj } from '@/configs/source'
import classNames from 'classnames'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'

import {
  ReactSketchCanvas,
  type ReactSketchCanvasRef,
} from 'react-sketch-canvas'

interface SignatureModalProps {
  open: boolean
  setOpen: (open: boolean) => void
  onSignatureComplete?: (signatureData: {
    imageData: string
    position: 'left-bottom' | 'right-bottom'
    size: 'small' | 'large'
  }) => void
}

export const SignatureModal: React.FC<SignatureModalProps> = ({
  open,
  setOpen,
  onSignatureComplete,
}) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const canvasRef = useRef<ReactSketchCanvasRef>(null)

  // 签名工具状态
  const [strokeColor, setStrokeColor] = useState('#000000')
  const [strokeWidth, setStrokeWidth] = useState(5) // 默认小字体
  const [signaturePosition, setSignaturePosition] = useState<
    'left-bottom' | 'right-bottom'
  >('right-bottom')
  const [signatureSize, setSignatureSize] = useState<'small' | 'large'>('small')

  // 预设颜色
  const colorOptions = [
    { color: '#000000', name: '黑色' },
    { color: '#FF0000', name: '红色' },
    { color: '#0000FF', name: '蓝色' },
    { color: '#008000', name: '绿色' },
  ]

  const handleColorSelect = (color: string) => {
    setStrokeColor(color)
  }

  const handleUndo = () => {
    canvasRef.current?.undo()
  }

  const handleClear = () => {
    canvasRef.current?.clearCanvas()
  }

  const handleSizeChange = (size: 'small' | 'large') => {
    setSignatureSize(size)
    setStrokeWidth(size === 'small' ? 5 : 15)
  }

  const handlePositionChange = (position: 'left-bottom' | 'right-bottom') => {
    setSignaturePosition(position)
  }

  const handleConfirm = async () => {
    try {
      if (canvasRef.current) {
        const imageData = await canvasRef.current.exportImage('png')
        onSignatureComplete?.({
          imageData,
          position: signaturePosition,
          size: signatureSize,
        })
        setOpen(false)
      }
    } catch (error) {
      console.error('导出签名失败:', error)
    }
  }

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 800 : 600}
      className="p-0 border-none"
      contentClassName="!p-0 border-none"
      content={
        <div className="flex flex-col h-full">
          {/* 标题 */}
          <div className="text-center py-6 border-b border-gray-200">
            <h2 className="text-[2.4rem] font-semibold maze-primary-text">
              {t('Please sign in the area below')}
            </h2>
          </div>

          {/* 签名区域 */}
          <div className="flex-1 p-6 flex justify-center items-center">
            <div className="w-full max-w-[500px] h-[300px] border-2 border-dashed border-gray-300 rounded-lg bg-white">
              <ReactSketchCanvas
                ref={canvasRef}
                strokeColor={strokeColor}
                strokeWidth={strokeWidth}
                canvasColor="transparent"
                width="100%"
                height="100%"
                style={{
                  borderRadius: '8px',
                }}
              />
            </div>
          </div>

          {/* 工具栏 */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex flex-wrap justify-center items-center gap-4">
              {/* 颜色选择 */}
              <div className="flex items-center gap-2">
                <SvgIcon
                  src={publicPreLoadSourceObj.detailColor}
                  svgClassName="w-8 h-8"
                />
                <div className="flex gap-2">
                  {colorOptions.map(option => (
                    <button
                      key={option.color}
                      className={classNames(
                        'w-8 h-8 rounded-full border-2 cursor-pointer',
                        strokeColor === option.color
                          ? 'border-blue-500 scale-110'
                          : 'border-gray-300'
                      )}
                      style={{ backgroundColor: option.color }}
                      onClick={() => handleColorSelect(option.color)}
                      title={option.name}
                    />
                  ))}
                </div>
              </div>

              {/* 撤销 */}
              <button
                className="flex items-center gap-1 px-3 py-2 rounded-lg hover:bg-gray-100"
                onClick={handleUndo}
                title={t('Undo')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailRedo}
                  svgClassName="w-6 h-6"
                />
                <span className="text-sm">{t('Undo')}</span>
              </button>

              {/* 清空 */}
              <button
                className="flex items-center gap-1 px-3 py-2 rounded-lg hover:bg-gray-100"
                onClick={handleClear}
                title={t('Clear')}
              >
                <SvgIcon
                  src={publicPreLoadSourceObj.detailClear}
                  svgClassName="w-6 h-6"
                />
                <span className="text-sm">{t('Clear')}</span>
              </button>

              {/* 位置选择 */}
              <div className="flex items-center gap-2">
                <span className="text-sm">{t('Position')}:</span>
                <button
                  className={classNames(
                    'px-3 py-1 rounded text-sm',
                    signaturePosition === 'left-bottom'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700'
                  )}
                  onClick={() => handlePositionChange('left-bottom')}
                >
                  {t('Bottom Left')}
                </button>
                <button
                  className={classNames(
                    'px-3 py-1 rounded text-sm',
                    signaturePosition === 'right-bottom'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700'
                  )}
                  onClick={() => handlePositionChange('right-bottom')}
                >
                  {t('Bottom Right')}
                </button>
              </div>

              {/* 字体大小 */}
              <div className="flex items-center gap-2">
                <button
                  className={classNames(
                    'flex items-center gap-1 px-3 py-2 rounded-lg',
                    signatureSize === 'small'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-100'
                  )}
                  onClick={() => handleSizeChange('small')}
                  title={t('Small Font')}
                >
                  <SvgIcon
                    src={publicPreLoadSourceObj.detailFontSmall}
                    svgClassName="w-6 h-6"
                  />
                </button>
                <button
                  className={classNames(
                    'flex items-center gap-1 px-3 py-2 rounded-lg',
                    signatureSize === 'large'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-100'
                  )}
                  onClick={() => handleSizeChange('large')}
                  title={t('Large Font')}
                >
                  <SvgIcon
                    src={publicPreLoadSourceObj.detailFontLarge}
                    svgClassName="w-6 h-6"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      }
      onOk={handleConfirm}
      okText={t('Confirm')}
      showOkButton={true}
      showCancelButton={false}
      onCancel={handleClose}
    />
  )
}

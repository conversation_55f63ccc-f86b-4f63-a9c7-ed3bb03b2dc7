interface ComposeSignatureParams {
  backgroundImageUrl: string
  signatureImageData: string // base64格式的签名图片
  position: 'left-bottom' | 'right-bottom'
  size: 'small' | 'large'
}

/**
 * 将签名图片与背景图片合成
 * @param params 合成参数
 * @returns 合成后的图片Blob
 */
export const composeSignatureImage = async (
  params: ComposeSignatureParams
): Promise<Blob> => {
  const { backgroundImageUrl, signatureImageData, position, size } = params

  return new Promise((resolve, reject) => {
    // 创建Canvas元素
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('无法创建Canvas上下文'))
      return
    }

    // 创建背景图片对象
    const backgroundImg = new Image()
    backgroundImg.crossOrigin = 'anonymous' // 处理跨域问题

    backgroundImg.onload = () => {
      try {
        // 设置Canvas尺寸为背景图片尺寸
        canvas.width = backgroundImg.width
        canvas.height = backgroundImg.height

        // 绘制背景图片
        ctx.drawImage(backgroundImg, 0, 0)

        // 创建签名图片对象
        const signatureImg = new Image()

        signatureImg.onload = () => {
          try {
            // 根据size参数确定签名图片尺寸
            const signatureSize = getSignatureSize(size)
            const signatureWidth = signatureSize.width
            const signatureHeight = signatureSize.height

            // 根据position参数计算签名位置
            const signaturePosition = calculateSignaturePosition(
              position,
              canvas.width,
              canvas.height,
              signatureWidth,
              signatureHeight
            )

            // 绘制签名图片
            ctx.drawImage(
              signatureImg,
              signaturePosition.x,
              signaturePosition.y,
              signatureWidth,
              signatureHeight
            )

            // 将Canvas转换为Blob
            canvas.toBlob(
              (blob) => {
                if (blob) {
                  resolve(blob)
                } else {
                  reject(new Error('Canvas转换为Blob失败'))
                }
              },
              'image/png',
              1.0
            )
          } catch (error) {
            reject(new Error(`签名图片处理失败: ${error}`))
          }
        }

        signatureImg.onerror = () => {
          reject(new Error('签名图片加载失败'))
        }

        // 加载签名图片
        signatureImg.src = signatureImageData
      } catch (error) {
        reject(new Error(`背景图片处理失败: ${error}`))
      }
    }

    backgroundImg.onerror = () => {
      reject(new Error('背景图片加载失败'))
    }

    // 加载背景图片
    backgroundImg.src = backgroundImageUrl
  })
}

/**
 * 根据size参数获取签名图片尺寸
 * @param size 尺寸类型
 * @returns 签名图片的宽高
 */
const getSignatureSize = (size: 'small' | 'large') => {
  switch (size) {
    case 'small':
      return { width: 128, height: 64 }
    case 'large':
      return { width: 176, height: 80 }
    default:
      return { width: 128, height: 64 }
  }
}

/**
 * 计算签名图片在背景图片上的位置
 * @param position 位置类型
 * @param backgroundWidth 背景图片宽度
 * @param backgroundHeight 背景图片高度
 * @param signatureWidth 签名图片宽度
 * @param signatureHeight 签名图片高度
 * @returns 签名图片的x,y坐标
 */
const calculateSignaturePosition = (
  position: 'left-bottom' | 'right-bottom',
  backgroundWidth: number,
  backgroundHeight: number,
  signatureWidth: number,
  signatureHeight: number
) => {
  const margin = 32 // 边距32px

  switch (position) {
    case 'left-bottom':
      return {
        x: margin,
        y: backgroundHeight - signatureHeight - margin,
      }
    case 'right-bottom':
      return {
        x: backgroundWidth - signatureWidth - margin,
        y: backgroundHeight - signatureHeight - margin,
      }
    default:
      return {
        x: backgroundWidth - signatureWidth - margin,
        y: backgroundHeight - signatureHeight - margin,
      }
  }
}
